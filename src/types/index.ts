import { ThemeVariant } from '@/lib/themes';

// Form Types
export interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  company: string;
  jobTitle?: string;
  message?: string;
  preferredContactMethod: 'email' | 'phone';
  consent: boolean;
}

export interface DemoRequestFormData extends ContactFormData {
  facilityType: 'hospital' | 'nursing-home' | 'clinic' | 'assisted-living' | 'other';
  facilitySize: 'small' | 'medium' | 'large' | 'enterprise';
  currentSystem?: string;
  timeline: 'immediate' | '1-3-months' | '3-6-months' | '6-12-months' | 'planning';
  specificRequirements?: string;
}

export interface ConsultationFormData extends ContactFormData {
  consultationType: 'technical' | 'commercial' | 'implementation' | 'support';
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  preferredDate?: string;
  preferredTime?: string;
  topics: string[];
}

export interface ConfiguratorFormData {
  facilityType: 'hospital' | 'nursing-home' | 'clinic' | 'assisted-living' | 'other';
  numberOfBeds: number;
  numberOfStations: number;
  coverage: 'basic' | 'standard' | 'premium' | 'enterprise';
  features: string[];
  budget?: 'under-50k' | '50k-100k' | '100k-250k' | '250k-500k' | 'over-500k';
  contactInfo: ContactFormData;
}

// Component Props
export interface SectionProps {
  theme?: ThemeVariant;
  className?: string;
}

export interface CTAProps {
  variant: 'primary' | 'secondary' | 'tertiary';
  size?: 'sm' | 'md' | 'lg';
  href?: string;
  onClick?: () => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  loading?: boolean;
}

// Content Types
export interface Testimonial {
  id: string;
  name: string;
  title: string;
  company: string;
  content: string;
  rating: number;
  image?: string;
  verified: boolean;
}

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
  benefits: string[];
  technicalSpecs?: Record<string, string>;
}

export interface UseCase {
  id: string;
  title: string;
  description: string;
  facilityType: string;
  challenges: string[];
  solutions: string[];
  results: string[];
  image?: string;
}

export interface Certification {
  id: string;
  name: string;
  description: string;
  logo: string;
  validUntil?: string;
  certificationBody: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface FormSubmissionResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  message: string;
  nextSteps?: string[];
}

// Error Types
export interface FormError {
  field: string;
  message: string;
  code?: string;
}

export interface ValidationError {
  errors: FormError[];
  message: string;
}

// Theme and Layout Types
export interface LayoutProps {
  children: React.ReactNode;
  theme?: ThemeVariant;
  showHeader?: boolean;
  showFooter?: boolean;
  className?: string;
}

export interface NavigationItem {
  label: string;
  href: string;
  external?: boolean;
  children?: NavigationItem[];
}

// Analytics and Tracking
export interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  customParameters?: Record<string, any>;
}

export interface ConversionGoal {
  id: string;
  name: string;
  type: 'form-submission' | 'page-view' | 'click' | 'download';
  value?: number;
  metadata?: Record<string, any>;
}
