'use client';

import { useTheme } from '@/components/theme-provider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { themes, ThemeVariant } from '@/lib/themes';

export function ThemeSwitcher() {
  const { theme, setTheme, availableThemes, mounted } = useTheme();

  if (!mounted) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Theme Selector</CardTitle>
          <CardDescription>
            Loading theme options...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            {Array.from({ length: 6 }).map((_, index) => (
              <div
                key={index}
                className="h-9 bg-muted rounded-md animate-pulse"
              />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Theme Selector</CardTitle>
        <CardDescription>
          Choose from 6 different theme variants to see how the design adapts.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          {availableThemes.map((themeVariant) => (
            <Button
              key={themeVariant}
              variant={theme === themeVariant ? "default" : "outline"}
              size="sm"
              onClick={() => setTheme(themeVariant)}
              className="justify-start"
            >
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{
                  backgroundColor: themes[themeVariant].colors.primary.replace('hsl(', '').replace(')', '').split(' ').length === 3 
                    ? `hsl(${themes[themeVariant].colors.primary.replace('hsl(', '').replace(')', '')})`
                    : themes[themeVariant].colors.primary
                }}
              />
              {themes[themeVariant].name}
            </Button>
          ))}
        </div>
        <div className="mt-4 p-3 bg-muted rounded-md">
          <p className="text-sm text-muted-foreground">
            Current theme: <span className="font-medium text-foreground">{themes[theme].name}</span>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
