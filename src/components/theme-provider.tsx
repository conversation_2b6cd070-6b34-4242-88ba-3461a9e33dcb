'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { ThemeVariant, applyTheme, getThemeFromUrl } from '@/lib/themes';

interface ThemeContextType {
  theme: ThemeVariant;
  setTheme: (theme: ThemeVariant) => void;
  availableThemes: ThemeVariant[];
  mounted: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: ThemeVariant;
}

export function ThemeProvider({ children, defaultTheme = 'default' }: ThemeProviderProps) {
  const [theme, setThemeState] = useState<ThemeVariant>(defaultTheme);
  const [mounted, setMounted] = useState(false);

  const availableThemes: ThemeVariant[] = ['default', 'purple', 'blue', 'green', 'amber', 'red'];

  useEffect(() => {
    setMounted(true);
    const urlTheme = getThemeFromUrl();
    if (urlTheme !== defaultTheme) {
      setThemeState(urlTheme);
    }
  }, [defaultTheme]);

  useEffect(() => {
    if (mounted) {
      applyTheme(theme);
    }
  }, [theme, mounted]);

  const setTheme = (newTheme: ThemeVariant) => {
    setThemeState(newTheme);

    // Update URL parameter
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      if (newTheme === 'default') {
        url.searchParams.delete('theme');
      } else {
        url.searchParams.set('theme', newTheme);
      }
      window.history.replaceState({}, '', url.toString());
    }
  };

  return (
    <ThemeContext.Provider value={{ theme, setTheme, availableThemes, mounted }}>
      {children}
    </ThemeContext.Provider>
  );
}
