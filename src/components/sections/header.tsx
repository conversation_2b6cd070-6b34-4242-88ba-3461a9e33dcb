'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger } from '@/components/ui/navigation-menu';
import { useTheme } from '@/components/theme-provider';
import { LanguageSwitcher } from '@/components/language-switcher';
import { Menu, X, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavigationItem {
  label: string;
  href: string;
  external?: boolean;
  children?: NavigationItem[];
}

function useNavigationItems() {
  const t = useTranslations('navigation');

  return [
    {
      label: t('services'),
      href: '#services',
      children: [
        { label: t('medpower'), href: '#medpower' },
        { label: t('telemonitoring'), href: '#telemonitoring' },
        { label: t('remoteCare'), href: '#remote-care' },
      ],
    },
    {
      label: t('features'),
      href: '#features',
      children: [
        { label: t('vitalData'), href: '#vital-data' },
        { label: t('alarmSystem'), href: '#alarm-system' },
        { label: t('patientSurveys'), href: '#patient-surveys' },
        { label: t('integration'), href: '#integration' },
      ],
    },
    { label: t('company'), href: '#company' },
    { label: t('contact'), href: '#contact' },
  ];
}

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { theme, setTheme, availableThemes, mounted } = useTheme();
  const t = useTranslations('common');
  const tCta = useTranslations('cta');
  const locale = useLocale();
  const navigationItems = useNavigationItems();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleMobileMenuClose = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <header
      className={cn(
        'sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-all duration-200',
        isScrolled && 'shadow-sm'
      )}
    >
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-3">
            <Link href={`/${locale}`} className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <span className="text-primary-foreground font-bold text-sm">A</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-bold text-foreground">{t('company')}</span>
                  <Badge variant="secondary" className="text-xs px-2 py-0 h-4">
                    {t('certification')}
                  </Badge>
                </div>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <NavigationMenu>
              <NavigationMenuList>
                {navigationItems.map((item) => (
                  <NavigationMenuItem key={item.label}>
                    {item.children ? (
                      <>
                        <NavigationMenuTrigger className="text-foreground hover:text-primary transition-colors">
                          {item.label}
                        </NavigationMenuTrigger>
                        <NavigationMenuContent>
                          <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                            {item.children.map((child) => (
                              <li key={child.label}>
                                <NavigationMenuLink asChild>
                                  <Link
                                    href={child.href}
                                    className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                  >
                                    <div className="text-sm font-medium leading-none">{child.label}</div>
                                  </Link>
                                </NavigationMenuLink>
                              </li>
                            ))}
                          </ul>
                        </NavigationMenuContent>
                      </>
                    ) : (
                      <NavigationMenuLink asChild>
                        <Link
                          href={item.href}
                          className="text-foreground hover:text-primary transition-colors px-3 py-2 text-sm font-medium"
                        >
                          {item.label}
                        </Link>
                      </NavigationMenuLink>
                    )}
                  </NavigationMenuItem>
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </nav>

          {/* Desktop CTAs, Language Switcher and Theme Switcher */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Language Switcher */}
            <LanguageSwitcher variant="select" />

            {/* Theme Switcher (temporary for development) */}
            {mounted && (
              <select
                value={theme}
                onChange={(e) => setTheme(e.target.value as any)}
                className="text-xs border border-border rounded px-2 py-1 bg-background"
              >
                {availableThemes.map((t) => (
                  <option key={t} value={t}>
                    {t}
                  </option>
                ))}
              </select>
            )}

            <Button variant="outline" size="sm" asChild>
              <Link href={`/${locale}/schedule-consultation`}>{tCta('scheduleConsultation')}</Link>
            </Button>
            <Button size="sm" asChild>
              <Link href={`/${locale}/request-demo`}>{tCta('requestDemo')}</Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="px-2">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                <div className="flex flex-col space-y-4 mt-8">
                  {/* Mobile Navigation */}
                  <nav className="flex flex-col space-y-4">
                    {navigationItems.map((item) => (
                      <div key={item.label}>
                        {item.children ? (
                          <div className="space-y-2">
                            <div className="font-medium text-foreground">{item.label}</div>
                            <div className="pl-4 space-y-2">
                              {item.children.map((child) => (
                                <Link
                                  key={child.label}
                                  href={child.href}
                                  className="block text-sm text-muted-foreground hover:text-primary transition-colors"
                                  onClick={handleMobileMenuClose}
                                >
                                  {child.label}
                                </Link>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <Link
                            href={item.href}
                            className="block font-medium text-foreground hover:text-primary transition-colors"
                            onClick={handleMobileMenuClose}
                          >
                            {item.label}
                          </Link>
                        )}
                      </div>
                    ))}
                  </nav>

                  {/* Mobile CTAs */}
                  <div className="flex flex-col space-y-3 pt-4 border-t border-border">
                    <Button variant="outline" asChild>
                      <Link href={`/${locale}/schedule-consultation`} onClick={handleMobileMenuClose}>
                        {tCta('scheduleConsultation')}
                      </Link>
                    </Button>
                    <Button asChild>
                      <Link href={`/${locale}/request-demo`} onClick={handleMobileMenuClose}>
                        {tCta('requestDemo')}
                      </Link>
                    </Button>
                  </div>

                  {/* Mobile Language Switcher */}
                  <div className="pt-4 border-t border-border">
                    <label className="text-sm font-medium text-foreground mb-2 block">
                      Language / Sprache / Język
                    </label>
                    <LanguageSwitcher variant="buttons" className="justify-start" />
                  </div>

                  {/* Mobile Theme Switcher */}
                  {mounted && (
                    <div className="pt-4 border-t border-border">
                      <label className="text-sm font-medium text-foreground mb-2 block">
                        Theme
                      </label>
                      <select
                        value={theme}
                        onChange={(e) => setTheme(e.target.value as any)}
                        className="w-full border border-border rounded px-3 py-2 bg-background text-sm"
                      >
                        {availableThemes.map((t) => (
                          <option key={t} value={t}>
                            {t}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}
