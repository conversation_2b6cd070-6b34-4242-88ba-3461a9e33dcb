export type ThemeVariant = 'default' | 'purple' | 'blue' | 'green' | 'amber' | 'red';

export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    primaryForeground: string;
    secondary: string;
    secondaryForeground: string;
    accent: string;
    accentForeground: string;
    muted: string;
    mutedForeground: string;
    background: string;
    foreground: string;
    card: string;
    cardForeground: string;
    border: string;
    input: string;
    ring: string;
  };
}

export const themes: Record<ThemeVariant, ThemeConfig> = {
  default: {
    name: 'Healthcare Blue',
    colors: {
      primary: 'hsl(217 91% 60%)', // Professional healthcare blue #2563eb
      primaryForeground: 'hsl(0 0% 100%)',
      secondary: 'hsl(210 40% 96%)',
      secondaryForeground: 'hsl(222.2 84% 4.9%)',
      accent: 'hsl(142 76% 36%)', // Trust-building green for certifications
      accentForeground: 'hsl(0 0% 100%)',
      muted: 'hsl(210 40% 96%)',
      mutedForeground: 'hsl(215.4 16.3% 46.9%)',
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      card: 'hsl(0 0% 100%)',
      cardForeground: 'hsl(222.2 84% 4.9%)',
      border: 'hsl(214.3 31.8% 91.4%)',
      input: 'hsl(214.3 31.8% 91.4%)',
      ring: 'hsl(217 91% 60%)',
    },
  },
  purple: {
    name: 'Medical Purple',
    colors: {
      primary: 'hsl(262 83% 58%)', // Medical purple for specialty care
      primaryForeground: 'hsl(0 0% 100%)',
      secondary: 'hsl(270 95% 95%)',
      secondaryForeground: 'hsl(262 83% 58%)',
      accent: 'hsl(142 76% 36%)', // Consistent green accent
      accentForeground: 'hsl(0 0% 100%)',
      muted: 'hsl(270 95% 95%)',
      mutedForeground: 'hsl(215.4 16.3% 46.9%)',
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(224 71% 4%)',
      card: 'hsl(0 0% 100%)',
      cardForeground: 'hsl(224 71% 4%)',
      border: 'hsl(270 95% 95%)',
      input: 'hsl(270 95% 95%)',
      ring: 'hsl(262 83% 58%)',
    },
  },
  blue: {
    name: 'Blue',
    colors: {
      primary: 'hsl(221.2 83.2% 53.3%)',
      primaryForeground: 'hsl(210 40% 98%)',
      secondary: 'hsl(214.3 31.8% 91.4%)',
      secondaryForeground: 'hsl(222.2 84% 4.9%)',
      accent: 'hsl(214.3 31.8% 91.4%)',
      accentForeground: 'hsl(222.2 84% 4.9%)',
      muted: 'hsl(214.3 31.8% 91.4%)',
      mutedForeground: 'hsl(215.4 16.3% 46.9%)',
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(222.2 84% 4.9%)',
      card: 'hsl(0 0% 100%)',
      cardForeground: 'hsl(222.2 84% 4.9%)',
      border: 'hsl(214.3 31.8% 91.4%)',
      input: 'hsl(214.3 31.8% 91.4%)',
      ring: 'hsl(221.2 83.2% 53.3%)',
    },
  },
  green: {
    name: 'Green',
    colors: {
      primary: 'hsl(142.1 76.2% 36.3%)',
      primaryForeground: 'hsl(355.7 100% 97.3%)',
      secondary: 'hsl(138.5 76.5% 96.7%)',
      secondaryForeground: 'hsl(142.1 84.2% 4.9%)',
      accent: 'hsl(138.5 76.5% 96.7%)',
      accentForeground: 'hsl(142.1 84.2% 4.9%)',
      muted: 'hsl(138.5 76.5% 96.7%)',
      mutedForeground: 'hsl(215.4 16.3% 46.9%)',
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(142.1 84.2% 4.9%)',
      card: 'hsl(0 0% 100%)',
      cardForeground: 'hsl(142.1 84.2% 4.9%)',
      border: 'hsl(138.5 76.5% 96.7%)',
      input: 'hsl(138.5 76.5% 96.7%)',
      ring: 'hsl(142.1 76.2% 36.3%)',
    },
  },
  amber: {
    name: 'Amber',
    colors: {
      primary: 'hsl(32.1 94.6% 43.7%)',
      primaryForeground: 'hsl(355.7 100% 97.3%)',
      secondary: 'hsl(39.3 100% 93.3%)',
      secondaryForeground: 'hsl(32.1 94.6% 43.7%)',
      accent: 'hsl(39.3 100% 93.3%)',
      accentForeground: 'hsl(32.1 94.6% 43.7%)',
      muted: 'hsl(39.3 100% 93.3%)',
      mutedForeground: 'hsl(215.4 16.3% 46.9%)',
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(20 14.3% 4.1%)',
      card: 'hsl(0 0% 100%)',
      cardForeground: 'hsl(20 14.3% 4.1%)',
      border: 'hsl(39.3 100% 93.3%)',
      input: 'hsl(39.3 100% 93.3%)',
      ring: 'hsl(32.1 94.6% 43.7%)',
    },
  },
  red: {
    name: 'Red',
    colors: {
      primary: 'hsl(0 72.2% 50.6%)',
      primaryForeground: 'hsl(355.7 100% 97.3%)',
      secondary: 'hsl(0 85.7% 97.3%)',
      secondaryForeground: 'hsl(0 72.2% 50.6%)',
      accent: 'hsl(0 85.7% 97.3%)',
      accentForeground: 'hsl(0 72.2% 50.6%)',
      muted: 'hsl(0 85.7% 97.3%)',
      mutedForeground: 'hsl(215.4 16.3% 46.9%)',
      background: 'hsl(0 0% 100%)',
      foreground: 'hsl(0 84% 4.9%)',
      card: 'hsl(0 0% 100%)',
      cardForeground: 'hsl(0 84% 4.9%)',
      border: 'hsl(0 85.7% 97.3%)',
      input: 'hsl(0 85.7% 97.3%)',
      ring: 'hsl(0 72.2% 50.6%)',
    },
  },
};

export function applyTheme(theme: ThemeVariant) {
  const themeConfig = themes[theme];
  const root = document.documentElement;
  
  Object.entries(themeConfig.colors).forEach(([key, value]) => {
    const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    root.style.setProperty(cssVar, value);
  });
}

export function getThemeFromUrl(): ThemeVariant {
  if (typeof window === 'undefined') return 'default';
  
  const params = new URLSearchParams(window.location.search);
  const theme = params.get('theme') as ThemeVariant;
  
  return theme && theme in themes ? theme : 'default';
}
