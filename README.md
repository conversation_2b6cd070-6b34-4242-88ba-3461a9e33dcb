# AITELMED Landing Page

A high-converting, professional landing page for AITELMED's DIN VDE-certified wireless nurse call systems built with Next.js 15, React 19, and TypeScript.

## 🚀 Features

- **Modern Tech Stack**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **6 Theme Variants**: De<PERSON>ult, <PERSON>, <PERSON>, Green, Amber, Red
- **Responsive Design**: Mobile-first approach with full responsive support
- **Accessibility**: WCAG 2.1 AA compliant with proper ARIA labels
- **Performance Optimized**: Core Web Vitals optimized with lazy loading
- **Form Validation**: Comprehensive validation with Zod schemas
- **SEO Optimized**: Proper meta tags and structured data

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Frontend**: React 19 with TypeScript (strict mode)
- **Styling**: Tailwind CSS with CSS custom properties
- **UI Components**: shadcn/ui component library
- **Forms**: react-hook-form with Zod validation
- **Animations**: Framer Motion (with reduced-motion support)

## 📁 Project Structure

```
/src
  /components
    /ui          # shadcn/ui components
    /sections    # Page sections
    /forms       # Form components
  /lib           # Utilities and configurations
  /types         # TypeScript definitions
  /styles        # Global styles and themes
/app
  /request-demo           # Demo request page
  /schedule-consultation  # Consultation page
  /configurator          # System configurator
```

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd draft1
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎨 Theme System

The application supports 6 theme variants that can be switched via URL parameter:

- Default: `http://localhost:3000`
- Purple: `http://localhost:3000?theme=purple`
- Blue: `http://localhost:3000?theme=blue`
- Green: `http://localhost:3000?theme=green`
- Amber: `http://localhost:3000?theme=amber`
- Red: `http://localhost:3000?theme=red`

## 📝 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🔧 Configuration

### Theme Customization
Themes are configured in `src/lib/themes.ts`. Each theme includes:
- Primary and secondary colors
- Background and foreground colors
- Border and accent colors
- Muted colors for secondary text

### Form Validation
Form schemas are defined in `src/lib/validations.ts` using Zod:
- Contact form validation
- Demo request validation
- Consultation scheduling validation
- File upload validation

## 🎯 Conversion Goals

The landing page is optimized for three primary conversion paths:

1. **Request a Free Demo** (`/request-demo`)
2. **Schedule a Consultation** (`/schedule-consultation`)
3. **Start Configurator** (`/configurator`)

## 🔒 Security Features

- Input sanitization and validation
- Rate limiting for form submissions
- Secure file upload handling
- XSS protection
- CSRF protection (to be implemented)

## 📱 Responsive Design

- Mobile-first approach
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Touch-friendly interface
- Optimized for all device sizes

## ♿ Accessibility

- WCAG 2.1 AA compliant
- Keyboard navigation support
- Screen reader compatibility
- High contrast ratios
- Focus indicators
- Reduced motion support

## 🚀 Performance

- Core Web Vitals optimized
- Image optimization with Next.js Image
- Code splitting and lazy loading
- Efficient caching strategies
- Minimal bundle size

## 📊 Development Status

### ✅ Completed (Task 1: Project Setup & Configuration)
- [x] Next.js 15 + React 19 + TypeScript setup
- [x] Tailwind CSS + shadcn/ui integration
- [x] 6 theme variants implementation
- [x] Project structure organization
- [x] Form validation schemas
- [x] Theme provider system
- [x] Basic routing setup
- [x] SEO metadata configuration

### 🔄 In Progress
- [ ] Design System & UI Components
- [ ] Hero Section Implementation
- [ ] Trust Indicators & Social Proof
- [ ] Product Features Section
- [ ] Contact Forms & CTAs
- [ ] Performance Optimization
- [ ] Testing & QA

## 🤝 Contributing

1. Follow the established code conventions
2. Use TypeScript with strict typing
3. Implement proper error handling
4. Add appropriate tests
5. Ensure accessibility compliance
6. Optimize for performance

## 📄 License

This project is proprietary to AITELMED.
